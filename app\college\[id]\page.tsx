"use client"

import { useState } from "react"
import {
  ArrowLeft,
  MapPin,
  Star,
  Building2,
  GraduationCap,
  Phone,
  Mail,
  Globe,
  Calendar,
  Trophy,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Share2,
  Heart,
  ChefHat,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import Link from "next/link"

// Mock college data
const collegeData = {
  id: "iit-bombay",
  name: "Indian Institute of Technology Bombay",
  shortName: "IIT Bombay",
  location: "Powai, Mumbai, Maharashtra",
  established: 1958,
  type: "Government",
  nirfRank: 3,
  rating: 4.8,
  reviews: 1247,
  logo: "/iit-bombay-logo.png",
  banner: "/iit-bombay-aerial.png",
  website: "https://www.iitb.ac.in",
  phone: "+91-22-2572-2545",
  email: "<EMAIL>",
  description:
    "Indian Institute of Technology Bombay is a public research university and technical institute in Powai, Mumbai, Maharashtra, India. IIT Bombay was founded in 1958.",
  highlights: [
    "Top 3 engineering institute in India",
    "Excellent placement record with 95% placement rate",
    "Strong alumni network in tech industry",
    "World-class research facilities",
  ],
  stats: {
    students: 11000,
    faculty: 650,
    departments: 18,
    placementRate: 95,
    averagePackage: "21.82 LPA",
    highestPackage: "1.8 CPA",
    fees: "2.18 LPA",
  },
}

const departments = [
  {
    id: "cse",
    name: "Computer Science & Engineering",
    students: 800,
    faculty: 45,
    rating: 4.9,
    reviews: 234,
    description: "Leading department in AI, ML, and software engineering research",
    labs: ["AI Lab", "Systems Lab", "Networks Lab", "HCI Lab"],
    collaborations: ["Google", "Microsoft", "IBM", "Intel"],
  },
  {
    id: "ece",
    name: "Electronics & Communication Engineering",
    students: 600,
    faculty: 38,
    rating: 4.7,
    reviews: 189,
    description: "Excellence in VLSI, embedded systems, and communication technologies",
    labs: ["VLSI Lab", "Communication Lab", "Embedded Systems Lab"],
    collaborations: ["Qualcomm", "Broadcom", "Texas Instruments"],
  },
  {
    id: "mechanical",
    name: "Mechanical Engineering",
    students: 700,
    faculty: 42,
    rating: 4.6,
    reviews: 156,
    description: "Focus on manufacturing, robotics, and thermal engineering",
    labs: ["Manufacturing Lab", "Robotics Lab", "Thermal Lab"],
    collaborations: ["Tata Motors", "Mahindra", "L&T"],
  },
]

const facultyData = [
  {
    id: 1,
    name: "Dr. Rajesh Kumar",
    designation: "Professor",
    department: "Computer Science",
    rating: 4.8,
    reviews: 89,
    tags: ["Chill", "Research Focused", "Helpful"],
    image: "/wise-professor.png",
  },
  {
    id: 2,
    name: "Dr. Priya Sharma",
    designation: "Associate Professor",
    department: "Computer Science",
    rating: 4.6,
    reviews: 67,
    tags: ["Strict", "Knowledgeable", "Good Teaching"],
    image: "/female-professor.png",
  },
]

const hostels = [
  {
    id: 1,
    name: "Hostel 1 (Boys)",
    type: "Single Occupancy",
    fees: "₹15,000/year",
    amenities: ["Wi-Fi", "Mess", "Common Room", "Laundry"],
    rules: "Entry till 11 PM, No outside food",
    image: "/hostel-room-interior.png",
  },
  {
    id: 2,
    name: "Hostel 3 (Girls)",
    type: "Double Occupancy",
    fees: "₹12,000/year",
    amenities: ["Wi-Fi", "Mess", "Study Room", "Security"],
    rules: "Entry till 10 PM, Visitor restrictions",
    image: "/girls-hostel-room.png",
  },
]

const fests = [
  {
    id: 1,
    name: "Mood Indigo",
    type: "Cultural",
    duration: "4 days",
    budget: "₹2 Crores",
    description: "Asia's largest college cultural festival",
    events: ["Music", "Dance", "Drama", "Literary", "Fine Arts"],
    image: "/college-cultural-fest-stage.png",
  },
  {
    id: 2,
    name: "Techfest",
    type: "Technical",
    duration: "3 days",
    budget: "₹1.5 Crores",
    description: "Premier technical festival with competitions and exhibitions",
    events: ["Robotics", "Coding", "Innovation", "Workshops"],
    image: "/technical-fest-robotics.png",
  },
]

const clubs = [
  {
    id: 1,
    name: "Coding Club",
    category: "Technical",
    members: 450,
    description: "Competitive programming and software development",
    achievements: ["ACM ICPC World Finals", "Google Summer of Code"],
    contact: "<EMAIL>",
  },
  {
    id: 2,
    name: "Music Club",
    category: "Cultural",
    members: 200,
    description: "Classical and contemporary music performances",
    achievements: ["National Youth Festival Winner", "Inter-IIT Champions"],
    contact: "<EMAIL>",
  },
]

export default function CollegeDetailPage({ params }: { params: { id: string } }) {
  const [activeTab, setActiveTab] = useState("overview")
  const [selectedDepartment, setSelectedDepartment] = useState<string | null>(null)

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/counseling/josaa-csab">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Results
                </Button>
              </Link>
              <div className="flex items-center space-x-2">
                <GraduationCap className="h-8 w-8 text-blue-600" />
                <span className="text-2xl font-bold text-gray-900">CollegeHub</span>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
              <Button variant="outline" size="sm">
                <Heart className="h-4 w-4 mr-2" />
                Save
              </Button>
              <Button variant="ghost">Login</Button>
            </div>
          </div>
        </div>
      </header>

      {/* College Banner */}
      <section className="relative">
        <img
          src={collegeData.banner || "/placeholder.svg"}
          alt={`${collegeData.name} campus`}
          className="w-full h-80 object-cover"
        />
        <div className="absolute inset-0 bg-black/40" />
        <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
          <div className="container mx-auto">
            <div className="flex items-end space-x-6">
              <img
                src={collegeData.logo || "/placeholder.svg"}
                alt={`${collegeData.name} logo`}
                className="w-24 h-24 rounded-lg border-4 border-white bg-white"
              />
              <div>
                <h1 className="text-4xl font-bold mb-2">{collegeData.name}</h1>
                <div className="flex items-center space-x-4 text-lg">
                  <div className="flex items-center space-x-1">
                    <MapPin className="h-5 w-5" />
                    <span>{collegeData.location}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-5 w-5" />
                    <span>Est. {collegeData.established}</span>
                  </div>
                  <Badge className="bg-white text-gray-900">{collegeData.type}</Badge>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-6 mb-8">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="academics">Academics</TabsTrigger>
            <TabsTrigger value="hostels">Hostels</TabsTrigger>
            <TabsTrigger value="fests">Fests & Events</TabsTrigger>
            <TabsTrigger value="clubs">Clubs</TabsTrigger>
            <TabsTrigger value="community">Community</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Main Info */}
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>About {collegeData.shortName}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-4">{collegeData.description}</p>
                    <div className="space-y-2">
                      {collegeData.highlights.map((highlight, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-blue-500 rounded-full" />
                          <span>{highlight}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Quick Stats */}
                <Card>
                  <CardHeader>
                    <CardTitle>Quick Statistics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-blue-600">#{collegeData.nirfRank}</div>
                        <div className="text-sm text-gray-600">NIRF Rank</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-emerald-600">{collegeData.stats.placementRate}%</div>
                        <div className="text-sm text-gray-600">Placement Rate</div>
                      </div>
                      <div className="text-3xl font-bold text-orange-600">{collegeData.stats.averagePackage}</div>
                      <div className="text-sm text-gray-600">Avg Package</div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-purple-600">{collegeData.stats.fees}</div>
                        <div className="text-sm text-gray-600">Annual Fees</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Rating */}
                <Card>
                  <CardHeader>
                    <CardTitle>Student Rating</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center mb-4">
                      <div className="text-4xl font-bold text-yellow-500 mb-2">{collegeData.rating}</div>
                      <div className="flex justify-center mb-2">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={star}
                            className={`h-5 w-5 ${
                              star <= collegeData.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                            }`}
                          />
                        ))}
                      </div>
                      <div className="text-gray-600">{collegeData.reviews.toLocaleString()} reviews</div>
                    </div>
                    <Button className="w-full">Write a Review</Button>
                  </CardContent>
                </Card>

                {/* Contact Info */}
                <Card>
                  <CardHeader>
                    <CardTitle>Contact Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <Phone className="h-5 w-5 text-gray-400" />
                      <span>{collegeData.phone}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Mail className="h-5 w-5 text-gray-400" />
                      <span>{collegeData.email}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Globe className="h-5 w-5 text-gray-400" />
                      <a href={collegeData.website} className="text-blue-600 hover:underline">
                        Official Website
                      </a>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Academics Tab */}
          <TabsContent value="academics" className="space-y-8">
            {!selectedDepartment ? (
              <div>
                <h2 className="text-3xl font-bold mb-6">Academic Departments</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {departments.map((dept) => (
                    <Card
                      key={dept.id}
                      className="cursor-pointer hover:shadow-lg transition-shadow"
                      onClick={() => setSelectedDepartment(dept.id)}
                    >
                      <CardHeader>
                        <CardTitle className="text-xl">{dept.name}</CardTitle>
                        <CardDescription>{dept.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Students:</span>
                            <span className="font-medium">{dept.students}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Faculty:</span>
                            <span className="font-medium">{dept.faculty}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-gray-600">Rating:</span>
                            <div className="flex items-center space-x-1">
                              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                              <span className="font-medium">{dept.rating}</span>
                              <span className="text-gray-500">({dept.reviews})</span>
                            </div>
                          </div>
                        </div>
                        <Button className="w-full mt-4">Explore Department</Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ) : (
              <div>
                <div className="flex items-center space-x-4 mb-6">
                  <Button variant="ghost" onClick={() => setSelectedDepartment(null)}>
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Departments
                  </Button>
                  <h2 className="text-3xl font-bold">{departments.find((d) => d.id === selectedDepartment)?.name}</h2>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Faculty Section */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Faculty Members</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {facultyData.map((faculty) => (
                        <div key={faculty.id} className="flex items-start space-x-4 p-4 border rounded-lg">
                          <Avatar>
                            <AvatarImage src={faculty.image || "/placeholder.svg"} />
                            <AvatarFallback>
                              {faculty.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <h4 className="font-semibold">{faculty.name}</h4>
                            <p className="text-gray-600 text-sm">{faculty.designation}</p>
                            <div className="flex items-center space-x-2 mt-2">
                              <div className="flex items-center space-x-1">
                                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                <span className="text-sm font-medium">{faculty.rating}</span>
                              </div>
                              <span className="text-gray-500 text-sm">({faculty.reviews} reviews)</span>
                            </div>
                            <div className="flex flex-wrap gap-1 mt-2">
                              {faculty.tags.map((tag) => (
                                <Badge key={tag} variant="secondary" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>

                  {/* Infrastructure */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Infrastructure & Labs</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {departments
                          .find((d) => d.id === selectedDepartment)
                          ?.labs.map((lab) => (
                            <div key={lab} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                              <Building2 className="h-5 w-5 text-blue-600" />
                              <span>{lab}</span>
                            </div>
                          ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Industry Collaborations */}
                <Card className="mt-6">
                  <CardHeader>
                    <CardTitle>Industry Collaborations</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {departments
                        .find((d) => d.id === selectedDepartment)
                        ?.collaborations.map((company) => (
                          <div key={company} className="text-center p-4 border rounded-lg">
                            <div className="font-medium">{company}</div>
                          </div>
                        ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>

          {/* Hostels Tab */}
          <TabsContent value="hostels" className="space-y-8">
            <h2 className="text-3xl font-bold">Accommodation</h2>

            {/* On-campus Hostels */}
            <div>
              <h3 className="text-2xl font-semibold mb-4">On-Campus Hostels</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {hostels.map((hostel) => (
                  <Card key={hostel.id}>
                    <CardHeader>
                      <img
                        src={hostel.image || "/placeholder.svg"}
                        alt={hostel.name}
                        className="w-full h-48 object-cover rounded-lg mb-4"
                      />
                      <CardTitle>{hostel.name}</CardTitle>
                      <CardDescription>{hostel.type}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Annual Fees:</span>
                          <span className="font-medium text-green-600">{hostel.fees}</span>
                        </div>
                        <div>
                          <span className="text-gray-600 block mb-2">Amenities:</span>
                          <div className="flex flex-wrap gap-2">
                            {hostel.amenities.map((amenity) => (
                              <Badge key={amenity} variant="outline">
                                {amenity}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div>
                          <span className="text-gray-600 block mb-1">Rules:</span>
                          <span className="text-sm">{hostel.rules}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Food & Dining */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <ChefHat className="h-6 w-6" />
                  <span>Food & Dining</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h4 className="font-semibold mb-2">Mess Timings</h4>
                    <div className="space-y-1 text-sm">
                      <div>Breakfast: 7:30 - 9:30 AM</div>
                      <div>Lunch: 12:00 - 2:00 PM</div>
                      <div>Snacks: 4:30 - 6:00 PM</div>
                      <div>Dinner: 7:30 - 10:00 PM</div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Nearby Restaurants</h4>
                    <div className="space-y-1 text-sm">
                      <div>Campus Food Court</div>
                      <div>Domino's Pizza</div>
                      <div>McDonald's</div>
                      <div>Local Dhabas</div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Delivery Policy</h4>
                    <div className="space-y-1 text-sm">
                      <div>Zomato/Swiggy allowed</div>
                      <div>Till 10:00 PM</div>
                      <div>ID verification required</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Fests & Events Tab */}
          <TabsContent value="fests" className="space-y-8">
            <h2 className="text-3xl font-bold">Festivals & Events</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {fests.map((fest) => (
                <Card key={fest.id}>
                  <CardHeader>
                    <img
                      src={fest.image || "/placeholder.svg"}
                      alt={fest.name}
                      className="w-full h-48 object-cover rounded-lg mb-4"
                    />
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-2xl">{fest.name}</CardTitle>
                      <Badge
                        className={
                          fest.type === "Cultural" ? "bg-purple-100 text-purple-800" : "bg-blue-100 text-blue-800"
                        }
                      >
                        {fest.type}
                      </Badge>
                    </div>
                    <CardDescription>{fest.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <span className="text-gray-600 block">Duration:</span>
                          <span className="font-medium">{fest.duration}</span>
                        </div>
                        <div>
                          <span className="text-gray-600 block">Budget:</span>
                          <span className="font-medium">{fest.budget}</span>
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-600 block mb-2">Event Categories:</span>
                        <div className="flex flex-wrap gap-2">
                          {fest.events.map((event) => (
                            <Badge key={event} variant="outline">
                              {event}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <Button className="w-full">View Past Events</Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Clubs Tab */}
          <TabsContent value="clubs" className="space-y-8">
            <h2 className="text-3xl font-bold">Clubs & Societies</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {clubs.map((club) => (
                <Card key={club.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>{club.name}</CardTitle>
                      <Badge variant="outline">{club.category}</Badge>
                    </div>
                    <CardDescription>{club.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Members:</span>
                        <span className="font-medium">{club.members}</span>
                      </div>
                      <div>
                        <span className="text-gray-600 block mb-2">Achievements:</span>
                        <div className="space-y-1">
                          {club.achievements.map((achievement) => (
                            <div key={achievement} className="flex items-center space-x-2">
                              <Trophy className="h-4 w-4 text-yellow-500" />
                              <span className="text-sm">{achievement}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                      <div className="pt-2">
                        <span className="text-gray-600 block">Contact:</span>
                        <span className="text-sm text-blue-600">{club.contact}</span>
                      </div>
                      <Button className="w-full">Join Club</Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Community Tab */}
          <TabsContent value="community" className="space-y-8">
            <div className="flex items-center justify-between">
              <h2 className="text-3xl font-bold">Community</h2>
              <Link href={`/college/${params.id}/reviews`}>
                <Button variant="outline">
                  <Star className="h-4 w-4 mr-2" />
                  View All Reviews
                </Button>
              </Link>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Q&A Forum */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <MessageSquare className="h-6 w-6" />
                    <span>Q&A Forum</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium mb-2">How is the placement scenario for CSE?</h4>
                      <p className="text-sm text-gray-600 mb-2">Asked by Anonymous • 2 hours ago</p>
                      <div className="flex items-center space-x-4">
                        <Button variant="ghost" size="sm">
                          <ThumbsUp className="h-4 w-4 mr-1" />
                          12
                        </Button>
                        <Button variant="ghost" size="sm">
                          <MessageSquare className="h-4 w-4 mr-1" />5 answers
                        </Button>
                      </div>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium mb-2">Best hostels for first year students?</h4>
                      <p className="text-sm text-gray-600 mb-2">Asked by Anonymous • 1 day ago</p>
                      <div className="flex items-center space-x-4">
                        <Button variant="ghost" size="sm">
                          <ThumbsUp className="h-4 w-4 mr-1" />8
                        </Button>
                        <Button variant="ghost" size="sm">
                          <MessageSquare className="h-4 w-4 mr-1" />3 answers
                        </Button>
                      </div>
                    </div>
                    <Button className="w-full">Ask a Question</Button>
                  </div>
                </CardContent>
              </Card>

              {/* Student Reviews */}
              <Card>
                <CardHeader>
                  <CardTitle>Recent Reviews</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star key={star} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          ))}
                        </div>
                        <span className="text-sm text-gray-600">• 2 days ago</span>
                      </div>
                      <p className="text-sm">
                        "Excellent faculty and infrastructure. The research opportunities are amazing and the campus
                        life is vibrant."
                      </p>
                      <div className="flex items-center space-x-4 mt-2">
                        <Button variant="ghost" size="sm">
                          <ThumbsUp className="h-4 w-4 mr-1" />
                          15
                        </Button>
                        <Button variant="ghost" size="sm">
                          <ThumbsDown className="h-4 w-4 mr-1" />2
                        </Button>
                      </div>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <div className="flex">
                          {[1, 2, 3, 4].map((star) => (
                            <Star key={star} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          ))}
                          <Star className="h-4 w-4 text-gray-300" />
                        </div>
                        <span className="text-sm text-gray-600">• 1 week ago</span>
                      </div>
                      <p className="text-sm">
                        "Great academics but hostel food could be better. Overall a good experience."
                      </p>
                      <div className="flex items-center space-x-4 mt-2">
                        <Button variant="ghost" size="sm">
                          <ThumbsUp className="h-4 w-4 mr-1" />8
                        </Button>
                        <Button variant="ghost" size="sm">
                          <ThumbsDown className="h-4 w-4 mr-1" />1
                        </Button>
                      </div>
                    </div>
                    <Textarea placeholder="Write your review..." className="mt-4" />
                    <Button className="w-full">Submit Review</Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Current Polls */}
            <Card>
              <CardHeader>
                <CardTitle>Current Polls</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h4 className="font-medium mb-3">Which fest do you enjoy the most?</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>Mood Indigo</span>
                        <div className="flex items-center space-x-2">
                          <Progress value={65} className="w-32" />
                          <span className="text-sm">65%</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>Techfest</span>
                        <div className="flex items-center space-x-2">
                          <Progress value={35} className="w-32" />
                          <span className="text-sm">35%</span>
                        </div>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mt-2">234 votes • Ends in 3 days</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
