import { Search, Filter, SortAsc, MapPin, Star, Users, Trophy, Building2, GraduationCap, ArrowLeft } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import Link from "next/link"

// Mock data for colleges
const mockColleges = [
  {
    id: "iit-bombay",
    name: "Indian Institute of Technology Bombay",
    location: "Mumbai, Maharashtra",
    logo: "/iit-bombay-logo.png",
    nirfRank: 3,
    placementRate: 95,
    averagePackage: "21.82 LPA",
    highestPackage: "1.8 CPA",
    fees: "2.18 LPA",
    type: "Government",
    established: 1958,
    rating: 4.8,
    reviews: 1247,
    branches: ["CSE", "ECE", "Mechanical", "Civil", "Chemical"],
    popular: true,
  },
  {
    id: "nit-trichy",
    name: "National Institute of Technology Tiruchirappalli",
    location: "Tiruchirappalli, Tamil Nadu",
    logo: "/nit-trichy-logo.png",
    nirfRank: 9,
    placementRate: 89,
    averagePackage: "18.5 LPA",
    highestPackage: "44 LPA",
    fees: "1.42 LPA",
    type: "Government",
    established: 1964,
    rating: 4.6,
    reviews: 892,
    branches: ["CSE", "ECE", "Mechanical", "Civil"],
    popular: true,
  },
  {
    id: "iiit-hyderabad",
    name: "International Institute of Information Technology Hyderabad",
    location: "Hyderabad, Telangana",
    logo: "/iiit-hyderabad-logo.png",
    nirfRank: 15,
    placementRate: 92,
    averagePackage: "25.6 LPA",
    highestPackage: "74 LPA",
    fees: "4.4 LPA",
    type: "Private",
    established: 1998,
    rating: 4.7,
    reviews: 634,
    branches: ["CSE", "ECE", "CSD"],
    popular: false,
  },
  {
    id: "dtu",
    name: "Delhi Technological University",
    location: "New Delhi, Delhi",
    logo: "/generic-university-logo.png",
    nirfRank: 36,
    placementRate: 85,
    averagePackage: "15.2 LPA",
    highestPackage: "1.2 CPA",
    fees: "1.58 LPA",
    type: "Government",
    established: 1941,
    rating: 4.4,
    reviews: 1156,
    branches: ["CSE", "ECE", "Mechanical", "Civil", "IT"],
    popular: true,
  },
  {
    id: "vit-vellore",
    name: "Vellore Institute of Technology",
    location: "Vellore, Tamil Nadu",
    logo: "/vit-vellore-logo.png",
    nirfRank: 18,
    placementRate: 78,
    averagePackage: "7.5 LPA",
    highestPackage: "41.6 LPA",
    fees: "4.08 LPA",
    type: "Private",
    established: 1984,
    rating: 4.2,
    reviews: 2341,
    branches: ["CSE", "ECE", "Mechanical", "Civil", "IT", "Biotech"],
    popular: false,
  },
  {
    id: "bits-pilani",
    name: "Birla Institute of Technology and Science, Pilani",
    location: "Pilani, Rajasthan",
    logo: "/bits-pilani-logo.png",
    nirfRank: 24,
    placementRate: 88,
    averagePackage: "16.8 LPA",
    highestPackage: "60 LPA",
    fees: "4.45 LPA",
    type: "Private",
    established: 1964,
    rating: 4.5,
    reviews: 987,
    branches: ["CSE", "ECE", "Mechanical", "Civil", "Chemical"],
    popular: true,
  },
]

const categoryInfo = {
  "josaa-csab": {
    title: "JOSAA/CSAB Counseling",
    description:
      "Joint Seat Allocation Authority for IITs, NITs, IIITs and other centrally funded technical institutions",
    totalColleges: 1247,
    color: "bg-blue-500",
  },
  "jac-delhi": {
    title: "JAC Delhi Counseling",
    description: "Joint Admission Counseling for Delhi University and other Delhi colleges",
    totalColleges: 89,
    color: "bg-emerald-500",
  },
  "comedk-kcet": {
    title: "COMEDK/KCET Counseling",
    description: "Karnataka engineering college admissions",
    totalColleges: 456,
    color: "bg-orange-500",
  },
  "mht-cet": {
    title: "MHT-CET Counseling",
    description: "Maharashtra engineering and medical college admissions",
    totalColleges: 623,
    color: "bg-purple-500",
  },
  wbjee: {
    title: "WBJEE Counseling",
    description: "West Bengal Joint Entrance Examination",
    totalColleges: 234,
    color: "bg-teal-500",
  },
}

export default function CounselingCategoryPage({ params }: { params: { category: string } }) {
  const category = categoryInfo[params.category as keyof typeof categoryInfo]

  if (!category) {
    return <div>Category not found</div>
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Home
                </Button>
              </Link>
              <div className="flex items-center space-x-2">
                <GraduationCap className="h-8 w-8 text-blue-600" />
                <span className="text-2xl font-bold text-gray-900">CollegeHub</span>
              </div>
            </div>

            <div className="hidden md:flex items-center space-x-3">
              <Button variant="ghost">Login</Button>
              <Button>Sign Up</Button>
            </div>
          </div>
        </div>
      </header>

      {/* Category Header */}
      <section className={`py-12 ${category.color} text-white`}>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl">
            <h1 className="text-4xl font-bold mb-4">{category.title}</h1>
            <p className="text-xl mb-6 opacity-90">{category.description}</p>
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Building2 className="h-5 w-5" />
                <span className="font-medium">{category.totalColleges.toLocaleString()} colleges</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span className="font-medium">50,000+ students</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Search and Filters */}
      <section className="py-8 bg-gray-50 border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            {/* Search */}
            <div className="flex-1 max-w-2xl">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <Input
                  placeholder="Search colleges by name, location, or branch..."
                  className="pl-12 py-3 border-2 border-gray-200 focus:border-blue-500"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="flex items-center space-x-4">
              <Select>
                <SelectTrigger className="w-48">
                  <SortAsc className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="rank">NIRF Ranking</SelectItem>
                  <SelectItem value="placement">Placement Rate</SelectItem>
                  <SelectItem value="package">Average Package</SelectItem>
                  <SelectItem value="fees">Fees (Low to High)</SelectItem>
                  <SelectItem value="rating">Student Rating</SelectItem>
                </SelectContent>
              </Select>

              <Select>
                <SelectTrigger className="w-48">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="College Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="government">Government</SelectItem>
                  <SelectItem value="private">Private</SelectItem>
                </SelectContent>
              </Select>

              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                More Filters
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Results */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold text-gray-900">Showing {mockColleges.length} colleges</h2>
            <div className="flex items-center space-x-2 text-gray-600">
              <span>View:</span>
              <Button variant="ghost" size="sm">
                Grid
              </Button>
              <Button variant="ghost" size="sm">
                List
              </Button>
            </div>
          </div>

          {/* College Cards */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {mockColleges.map((college) => (
              <Card
                key={college.id}
                className="hover:shadow-lg transition-shadow duration-300 border-2 hover:border-blue-200"
              >
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-4">
                      <img
                        src={college.logo || "/placeholder.svg"}
                        alt={`${college.name} logo`}
                        className="w-16 h-16 rounded-lg object-cover border"
                      />
                      <div>
                        <CardTitle className="text-xl font-bold text-gray-900 mb-2 leading-tight">
                          {college.name}
                        </CardTitle>
                        <div className="flex items-center space-x-2 text-gray-600">
                          <MapPin className="h-4 w-4" />
                          <span>{college.location}</span>
                        </div>
                      </div>
                    </div>
                    {college.popular && <Badge className="bg-yellow-100 text-yellow-800">Popular</Badge>}
                  </div>
                </CardHeader>

                <CardContent>
                  {/* Quick Stats */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">#{college.nirfRank}</div>
                      <div className="text-sm text-gray-600">NIRF Rank</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-emerald-600">{college.placementRate}%</div>
                      <div className="text-sm text-gray-600">Placement</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">{college.averagePackage}</div>
                      <div className="text-sm text-gray-600">Avg Package</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">{college.fees}</div>
                      <div className="text-sm text-gray-600">Annual Fees</div>
                    </div>
                  </div>

                  {/* Rating and Reviews */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center space-x-1">
                        <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                        <span className="font-semibold">{college.rating}</span>
                      </div>
                      <span className="text-gray-600">({college.reviews.toLocaleString()} reviews)</span>
                    </div>
                    <Badge variant="outline">{college.type}</Badge>
                  </div>

                  {/* Branches */}
                  <div className="mb-6">
                    <div className="text-sm text-gray-600 mb-2">Available Branches:</div>
                    <div className="flex flex-wrap gap-2">
                      {college.branches.slice(0, 4).map((branch) => (
                        <Badge key={branch} variant="secondary" className="text-xs">
                          {branch}
                        </Badge>
                      ))}
                      {college.branches.length > 4 && (
                        <Badge variant="secondary" className="text-xs">
                          +{college.branches.length - 4} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between">
                    <Link href={`/college/${college.id}`}>
                      <Button className="flex-1 mr-2">View Details</Button>
                    </Link>
                    <Button variant="outline" size="sm">
                      Compare
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Trophy className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              Load More Colleges
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
